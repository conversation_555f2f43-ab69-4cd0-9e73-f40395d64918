#include "HeadPoseCalibrator.h"
#include <chrono>

namespace tongxing {

HeadPoseCalibrator::HeadPoseCalibrator(int queue_size,
                                       int queue_start_frame,
                                       int k_num,
                                       float cluster_radius,
                                       float threshold_50f,
                                       float threshold_100f,
                                       float threshold_longer,
                                       float eye_cluster_radius,
                                       float eye_threshold_50f,
                                       float eye_threshold_100f,
                                       float eye_threshold_longer)
        : headqueue_size_(queue_size), queue_start_frame_(queue_start_frame), k_num_(k_num),
          cluster_radius_(cluster_radius), threshold_50f_(threshold_50f),
          threshold_100f_(threshold_100f), threshold_longerf_(threshold_longer),
          eye_cluster_radius_(eye_cluster_radius), eye_threshold_50f_(eye_threshold_50f),
          eye_threshold_100f_(eye_threshold_100f), eye_threshold_longerf_(eye_threshold_longer),
          frames_lvl1_(100), frames_lvl2_(200), frames_lvl3_(300),
          process_counter_(0) {}

void HeadPoseCalibrator::changeparam(float cluster_radius) {
    cluster_radius_ = cluster_radius;
}

void HeadPoseCalibrator::setTimeFrameThresholds(size_t frames_lvl1, size_t frames_lvl2, size_t frames_lvl3) {
    frames_lvl1_ = frames_lvl1;
    frames_lvl2_ = frames_lvl2;
    frames_lvl3_ = frames_lvl3;
}

void HeadPoseCalibrator::init() {
    headdata_deque_.resize(headqueue_size_ + 1);
    headdata_deque_.clear();
    headdis_matrix.reserve(headqueue_size_ + 1);
    headdis_matrix.clear();

    leyedata_deque_.resize(headqueue_size_ + 1);
    leyedata_deque_.clear();
    leyedis_matrix.reserve(headqueue_size_ + 1);
    leyedis_matrix.clear();

    reyedata_deque_.resize(headqueue_size_ + 1);
    reyedata_deque_.clear();
    reyedis_matrix.reserve(headqueue_size_ + 1);
    reyedis_matrix.clear();

    thelast_head_centroid.clear();
    thelast_leye_centroid.clear();
    thelast_reye_centroid.clear();
    process_counter_ = 0;
    head_cali_ok_ = false;
    leye_cali_ok_ = false;
    reye_cali_ok_ = false;
    global_cali_finish = false;
}

void HeadPoseCalibrator::quickFilling(std::vector<float>& head_angles,
                                std::vector<float>& lefteye_angles,
                                std::vector<float>& righteye_angles,
                                const float leye_uper_curve_score,
                                const float reye_uper_curve_score) {
    bool head_reasonable = areAnglesReasonable(head_angles);
    bool leye_reasonable = areAnglesReasonable(lefteye_angles);
    bool reye_reasonable = areAnglesReasonable(righteye_angles);

    for (int i = 0; i < headqueue_size_ - 1; i++) {
        if (head_reasonable) {
            addNewPoint(head_angles, headdata_deque_, headdis_matrix);
        }
        if (leye_reasonable) {
            addNewPoint(lefteye_angles, leyedata_deque_, leyedis_matrix);
            leye_uper_curve_data.push_back(leye_uper_curve_score);
            if (leye_uper_curve_data.size() > headqueue_size_) {
                leye_uper_curve_data.pop_front();
            }
        }
        if (reye_reasonable) {
            addNewPoint(righteye_angles, reyedata_deque_, reyedis_matrix);
            reye_uper_curve_data.push_back(reye_uper_curve_score);
            if (reye_uper_curve_data.size() > headqueue_size_) {
                reye_uper_curve_data.pop_front();
            }
        }
        process_counter_++;
    }

    return;
}

inline float euclideanDistance(const std::vector<float>& a, const std::vector<float>& b) {
    float sum = 0.0;
    for (size_t i = 0; i < a.size(); ++i) {
        float diff = a[i] - b[i];
        sum += diff * diff;
    }
    return std::sqrt(sum);
}

void HeadPoseCalibrator::addNewPoint(const std::vector<float>& new_point,
                                     std::deque<std::vector<float>>& data_deque_,
                                     std::vector<std::vector<float>>& distance_matrix) {
    size_t n = data_deque_.size();

    // 先将新点添加到数据队列
    data_deque_.push_back(new_point);
    
    // 重新计算完整的距离矩阵最后一行和一列
    std::vector<float> new_distances;
    new_distances.reserve(headqueue_size_ + 1);
    for (size_t i = 0; i < n; ++i) {
        float distance = euclideanDistance(new_point, data_deque_[i]); 
        new_distances.push_back(distance);
    }
    // 添加新点到自己的距离（对角线元素）
    new_distances.push_back(0.0f);

    // 添加新行
    distance_matrix.push_back(new_distances);
    // 为现有行添加新列
    for (size_t i = 0; i < n; ++i) {
        distance_matrix[i].push_back(new_distances[i]);
    }

    // 如果超过窗口大小，移除最旧的数据点
    if (data_deque_.size() > headqueue_size_) {
        // 移除最旧的数据点
        data_deque_.pop_front();

        // 移除距离矩阵中对应的行和列
        distance_matrix.erase(distance_matrix.begin());
        for (auto& row : distance_matrix) {
            row.erase(row.begin());
        }
    }
}

// 聚集度判断
bool HeadPoseCalibrator::aggregationCheck(std::deque<std::vector<float>>& data_deque_,
                                          std::vector<std::vector<float>>& dis_matrix,
                                          float cluster_radius_,
                                          float threshold,
                                          std::pair<size_t, std::vector<size_t>>& founded_index,
                                          float& current_max_ratio) {
    // 1. 对数据点进行一轮聚集程度的筛选，找出满足条件的点集
    // 根据距离矩阵计算聚集程度
    size_t n = data_deque_.size();
    std::vector<size_t> counts;

    bool found = false;
    float max_ratio = 0.0;
    float ratio = 0.0;

    for (size_t i = 0; i < n; ++i) {
        counts.clear();

        for (size_t j = 0; j < n; ++j) {
            if (i == j)
                continue;
            // 添加边界检查以防越界访问
            if (i < dis_matrix.size() && j < dis_matrix[i].size() &&
                dis_matrix[i][j] <= cluster_radius_) {
                counts.push_back(j);  // 存储符合条件的点的索引
                // std::cout << "headdis_matrix[i][j]:" << headdis_matrix[i][j] << std::endl;
            }
        }
        ratio = static_cast<float>(counts.size()) / (n - 1);
        if (ratio >= threshold) {
            found = true;
        }
        if (ratio >= max_ratio) {
            max_ratio = ratio;
            founded_index = {i, counts};  // 找出符合条件最多的
        }
    }

    // 返回当前最大比值
    current_max_ratio = max_ratio;

    // std::cout << "ratio:" << ratio << " counts.size():" << counts.size() << " n:" << n << std::endl;
    // std::cout << "ratio:" << max_ratio << " " << threshold << std::endl;
    return found;
}

bool HeadPoseCalibrator::execute(std::vector<float>& head_angles,
                                 std::vector<float>& lefteye_angles,
                                 std::vector<float>& righteye_angles,
                                 const float leye_uper_curve_score,
                                 const float reye_uper_curve_score,
                                 std::vector<float>& centroid_result,
                                 std::vector<float>& leye_centroid_result,
                                 std::vector<float>& reye_centroid_result,
                                 float& leye_uper_curve_score_mean,
                                 float& reye_uper_curve_score_mean,
                                 calistatus& status) {
    // 获取日志系统实例
    auto& logger = CalibrationLogger::getInstance();

    // 重置传入参数地址
    status.head_cali_finish = false;
    status.leye_cali_finish = false;
    status.reye_cali_finish = false;
    centroid_result.clear();
    leye_centroid_result.clear();
    reye_centroid_result.clear();
    leye_uper_curve_score_mean = 1.0f;
    reye_uper_curve_score_mean = 1.0f;

    // 1. 处理头部数据
    if (areAnglesReasonable(head_angles)) {
        bool head_update_is_reasonable = true;
        if (global_cali_finish && thelast_head_centroid.size() > 0) {
            const float head_dis = euclideanDistance(thelast_head_centroid, head_angles);
            if (head_dis > KMaxHeadUpdateDistance) {
                head_update_is_reasonable = false;
                // 记录头部更新距离过大
                logger.recordFilter("HEAD_UPDATE_DISTANCE_TOO_LARGE", "Distance " + std::to_string(head_dis) + ">" + std::to_string(KMaxHeadUpdateDistance), "head");
            }
        }

        bool head_buffer_full = (headdata_deque_.size() >= headqueue_size_);
        if (!head_buffer_full || !global_cali_finish || head_update_is_reasonable) {
            addNewPoint(head_angles, headdata_deque_, headdis_matrix);
        }
    } else {
        // 记录头部角度不合理
        std::string angle_details = "";
        if (head_angles.size() >= 3) {
            angle_details = std::to_string(head_angles[0]) + "," + std::to_string(head_angles[1]) + "," + std::to_string(head_angles[2]);
        }
        logger.recordFilter("HEAD_ANGLE_OUT_OF_RANGE", "Angle(" + angle_details + ") exceeds [-50°,50°]", "head");
    }

    // 2. 处理左眼数据
    if (areAnglesReasonable(lefteye_angles)) {
        bool leye_update_is_reasonable = true;
        if (global_cali_finish) {
            leye_update_is_reasonable = isUpdateReasonable(thelast_leye_centroid, lefteye_angles);
            if (!leye_update_is_reasonable) {
                logger.recordFilter("LEFT_EYE_UPDATE_CHANGE_TOO_LARGE", "Left eye update change exceeds reasonable range", "left_eye");
            }
        }

        bool leye_buffer_full = (leyedata_deque_.size() >= headqueue_size_);
        if (!leye_buffer_full || !global_cali_finish || leye_update_is_reasonable) {
            addNewPoint(lefteye_angles, leyedata_deque_, leyedis_matrix);
            leye_uper_curve_data.push_back(leye_uper_curve_score);
            if (leye_uper_curve_data.size() > headqueue_size_) {
                leye_uper_curve_data.pop_front();
            }
        }
    } else {
        // 记录左眼角度不合理
        std::string angle_details = "";
        if (lefteye_angles.size() >= 2) {
            angle_details = std::to_string(lefteye_angles[0]) + "," + std::to_string(lefteye_angles[1] * 10);
        }
        logger.recordFilter("LEFT_EYE_ANGLE_OUT_OF_RANGE", "Angle(" + angle_details + ") exceeds [-50°,50°]", "left_eye");
    }
    
    // 3. 处理右眼数据
    if (areAnglesReasonable(righteye_angles)) {
        bool reye_update_is_reasonable = true;
        if (global_cali_finish) {
            reye_update_is_reasonable = isUpdateReasonable(thelast_reye_centroid, righteye_angles);
            if (!reye_update_is_reasonable) {
                logger.recordFilter("RIGHT_EYE_UPDATE_CHANGE_TOO_LARGE", "Right eye update change exceeds reasonable range", "right_eye");
            }
        }

        bool reye_buffer_full = (reyedata_deque_.size() >= headqueue_size_);
        if (!reye_buffer_full || !global_cali_finish || reye_update_is_reasonable) {
            addNewPoint(righteye_angles, reyedata_deque_, reyedis_matrix);
            reye_uper_curve_data.push_back(reye_uper_curve_score);
            if (reye_uper_curve_data.size() > headqueue_size_ ) {
                reye_uper_curve_data.pop_front();
            }
        }
    } else {
        // 记录右眼角度不合理
        std::string angle_details = "";
        if (righteye_angles.size() >= 2) {
            angle_details = std::to_string(righteye_angles[0]) + "," + std::to_string(righteye_angles[1] * 10);
        }
        logger.recordFilter("RIGHT_EYE_ANGLE_OUT_OF_RANGE", "Angle(" + angle_details + ") exceeds [-50°,50°]", "right_eye");
    }

    if (headdata_deque_.size() <= queue_start_frame_ - 10) {
        // 记录缓冲区数据不足
        logger.recordFilter("BUFFER_INSUFFICIENT", "Head buffer " + std::to_string(headdata_deque_.size()) +
                           "<=" + std::to_string(queue_start_frame_ - 10), "head");
        return false;
    }

    // 每10帧判断一次是否标定成功,如果已经标定过一次，则100帧修正一次
    process_counter_++;
    if (global_cali_finish) {
        if (process_counter_ < 100) {
            // 记录处理频率限制(已标定完成)
            logger.recordFilter("PROCESSING_FREQUENCY_LIMIT", "Calibrated," + std::to_string(process_counter_) + "<100");
            return false;
        }
    } else {
        if (process_counter_ < 10) {
            // 记录处理频率限制(未标定完成)
            logger.recordFilter("PROCESSING_FREQUENCY_LIMIT", "Not calibrated," + std::to_string(process_counter_) + "<10");
            return false;
        }
    }

    process_counter_ = 0;

    // 阈值会随着累积的帧数平滑地变化
    float head_threshold = calculateDynamicThreshold(headdata_deque_.size(), threshold_50f_, threshold_100f_, threshold_longerf_);
    if (headdata_deque_.size() >= headqueue_size_ && !head_cali_ok_) {
        head_threshold = 0.1;
    }

    float leye_threshold = calculateDynamicThreshold(leyedata_deque_.size(), eye_threshold_50f_, eye_threshold_100f_, eye_threshold_longerf_);
    if (leyedata_deque_.size() >= headqueue_size_ && !leye_cali_ok_) {
        leye_threshold = 0.1;
    }

    float reye_threshold = calculateDynamicThreshold(reyedata_deque_.size(), eye_threshold_50f_, eye_threshold_100f_, eye_threshold_longerf_);
    if (reyedata_deque_.size() >= headqueue_size_ && !reye_cali_ok_) {
        reye_threshold = 0.1;
    }

    // 记录当前阈值信息到日志系统
    logger.recordThresholds(head_threshold, leye_threshold, reye_threshold);

    // std::cout << "headdata_deque_.size():" << headdata_deque_.size() << " head_threshold:" << head_threshold << std::endl;
    // std::cout << "leyedata_deque_.size():" << leyedata_deque_.size() << " leye_threshold:" << leye_threshold << std::endl;
    // std::cout << "reyedata_deque_.size():" << reyedata_deque_.size() << " reye_threshold:" << reye_threshold << std::endl;


    // 聚集度判断
    std::pair<size_t, std::vector<size_t>> founded_index;
    std::pair<size_t, std::vector<size_t>> leyefounded_index;
    std::pair<size_t, std::vector<size_t>> reyefounded_index;
    std::vector<size_t> leyecounts;
    std::vector<size_t> reyecounts;

    bool cali_status = false;
    bool head_found = false;
    bool reye_found = false;
    bool leye_found = false;

    // 当前聚集度比值
    float head_current_ratio = -1.0f;
    float leye_current_ratio = -1.0f;
    float reye_current_ratio = -1.0f;

    head_found = aggregationCheck(headdata_deque_, headdis_matrix, cluster_radius_, head_threshold,
                                  founded_index, head_current_ratio);

    // 对眼睛使用同样的聚集度判断
    leye_found = aggregationCheck(leyedata_deque_, leyedis_matrix, eye_cluster_radius_,
                                  leye_threshold, leyefounded_index, leye_current_ratio);
    reye_found = aggregationCheck(reyedata_deque_, reyedis_matrix, eye_cluster_radius_,
                                  reye_threshold, reyefounded_index, reye_current_ratio);

    // 记录当前聚集度比值到日志系统
    logger.recordCurrentRatios(head_current_ratio, leye_current_ratio, reye_current_ratio);

    // 当有一项符合要求时返回true
    if (head_found || leye_found || reye_found) {
        cali_status = true;
    }

    std::vector<std::vector<float>> selected_points;
    std::vector<std::vector<float>> leye_selected_points;
    std::vector<std::vector<float>> reye_selected_points;

    std::vector<float> leye_selected_curve_scores;
    std::vector<float> reye_selected_curve_scores;

    if (head_found) {
        for (auto index : founded_index.second) {
            selected_points.push_back(headdata_deque_[index]);
        }
        // std::cout << "selected_points.size():" << selected_points.size() << std::endl;
    }
    if (leye_found) {
        for (auto index : leyefounded_index.second) {
            leye_selected_points.push_back(leyedata_deque_[index]);
            if (leye_uper_curve_data[index] < KMaxFrontCurveScore && leye_uper_curve_data[index] > KMinFrontCurveScore) 
                leye_selected_curve_scores.push_back(leye_uper_curve_data[index]);
        }
        // std::cout << "leye_selected_points.size():" << leye_selected_points.size() << std::endl;
    }
    if (reye_found) {
        for (auto index : reyefounded_index.second) {
            reye_selected_points.push_back(reyedata_deque_[index]);
            if (reye_uper_curve_data[index] < KMaxFrontCurveScore && reye_uper_curve_data[index] > KMinFrontCurveScore)
                reye_selected_curve_scores.push_back(reye_uper_curve_data[index]);
        }
    }
    // std::cout << "reye_selected_points.size():" << reye_selected_points.size() << std::endl;

    std::vector<float> centroid(3, 0.0);
    if (head_found) {
        // 2.对筛选出的点集进行 k-means 聚类
        cv::Mat data_mat(static_cast<int>(selected_points.size()), 3, CV_32F);
        for (size_t i = 0; i < selected_points.size(); ++i) {
            data_mat.at<float>(static_cast<int>(i), 0) = static_cast<float>(selected_points[i][0]);
            data_mat.at<float>(static_cast<int>(i), 1) = static_cast<float>(selected_points[i][1]);
            data_mat.at<float>(static_cast<int>(i), 2) = static_cast<float>(selected_points[i][2]);
        }

        cv::Mat labels;
        cv::Mat centers;

        int attempts = 10;
        cv::TermCriteria criteria =
            cv::TermCriteria(cv::TermCriteria::MAX_ITER + cv::TermCriteria::EPS, 100, 1e-4);
        cv::kmeans(data_mat, k_num_, labels, criteria, attempts, cv::KMEANS_PP_CENTERS, centers);

        // 找到包含最多数据点的簇
        std::vector<int> cluster_counts(k_num_, 0);
        for (int i = 0; i < labels.rows; ++i) {
            int label = labels.at<int>(i, 0);
            cluster_counts[label]++;
        }
        int max_cluster_index = std::distance(
            cluster_counts.begin(), std::max_element(cluster_counts.begin(), cluster_counts.end()));
        int max_cluster_count = cluster_counts[max_cluster_index];

        // 获取质心坐标
        cv::Mat centroid_mat = centers.row(max_cluster_index);
        centroid = {static_cast<float>(centroid_mat.at<float>(0, 0)),
                    static_cast<float>(centroid_mat.at<float>(0, 1)),
                    static_cast<float>(centroid_mat.at<float>(0, 2))};
    }

    // 计算符合条件的左右眼角度均值
    std::vector<float> leye_centroid(2, 0.0);
    std::vector<float> reye_centroid(2, 0.0);
    float leye_curve_score_mean = 0.0f;
    float reye_curve_score_mean = 0.0f;

    if (leye_found) {
        size_t num_leye_points = leye_selected_points.size();
        if (num_leye_points > 0) {
            for (const auto& point : leye_selected_points) {
                leye_centroid[0] += point[0];
                leye_centroid[1] += point[1];
            }
            leye_centroid[0] /= num_leye_points;
            leye_centroid[1] /= num_leye_points;
        }

        size_t num_leye_curve_scores = leye_selected_curve_scores.size();
        if (num_leye_curve_scores > 0) {
            for (const auto& score : leye_selected_curve_scores) {
                leye_curve_score_mean += score;
            }
            leye_curve_score_mean /= num_leye_curve_scores;
        }
    }
    if (reye_found) {
        size_t num_reye_points = reye_selected_points.size();
        if (num_reye_points > 0) {
            for (const auto& point : reye_selected_points) {
                reye_centroid[0] += point[0];
                reye_centroid[1] += point[1];
            }
            reye_centroid[0] /= num_reye_points;
            reye_centroid[1] /= num_reye_points;
        }

        size_t num_reye_curve_scores = reye_selected_curve_scores.size();
        if (num_reye_curve_scores > 0) {
            for (const auto& score : reye_selected_curve_scores) {
                reye_curve_score_mean += score;
            }
            reye_curve_score_mean /= num_reye_curve_scores;
        }
    }

    // 保存第一次标定成功时的结果
    if (!global_cali_finish && cali_status) {
        thelast_head_centroid = centroid;
        thelast_leye_centroid = leye_centroid;
        thelast_reye_centroid = reye_centroid;
    }

    if (head_found) head_cali_ok_ = true;
    if (leye_found) leye_cali_ok_ = true;
    if (reye_found) reye_cali_ok_ = true;

    if (head_cali_ok_ && leye_cali_ok_ && reye_cali_ok_) {
        global_cali_finish = true;
        // 记录标定完成
        logger.recordSuccess(true, true, true);
        // 记录缓冲区大小
        logger.recordProgress(headdata_deque_.size(), leyedata_deque_.size(), reyedata_deque_.size());
    }

    // 返回结果
    centroid_result = centroid;
    leye_centroid_result = leye_centroid;
    reye_centroid_result = reye_centroid;
    leye_uper_curve_score_mean = leye_curve_score_mean;
    reye_uper_curve_score_mean = reye_curve_score_mean;

    status.head_cali_finish = head_found;
    status.leye_cali_finish = leye_found;
    status.reye_cali_finish = reye_found;

    // 日志系统会自动管理时间间隔

    return cali_status;
}

void HeadPoseCalibrator::clear() {
    headdata_deque_.clear();
    reyedata_deque_.clear();
    leyedata_deque_.clear();
    headdis_matrix.clear();
    leyedis_matrix.clear();
    reyedis_matrix.clear();
    thelast_head_centroid.clear();
    thelast_leye_centroid.clear();
    thelast_reye_centroid.clear();

    process_counter_ = 0;
    head_cali_ok_ = false;
    leye_cali_ok_ = false;
    reye_cali_ok_ = false;
    global_cali_finish = false;
}

float HeadPoseCalibrator::calculateDynamicThreshold(size_t frame_count, float threshold_50, float threshold_100, float threshold_longer) {
    if (frame_count <= frames_lvl1_) {
        return threshold_50;
    }
    if (frame_count > frames_lvl1_ && frame_count <= frames_lvl2_) {
        float ratio = static_cast<float>(frame_count - frames_lvl1_) / (frames_lvl2_ - frames_lvl1_);
        return threshold_50 + ratio * (threshold_100 - threshold_50);
    }
    if (frame_count > frames_lvl2_ && frame_count <= frames_lvl3_) {
        float ratio = static_cast<float>(frame_count - frames_lvl2_) / (frames_lvl3_ - frames_lvl2_);
        return threshold_100 + ratio * (threshold_longer - threshold_100);
    }
    
    return threshold_longer;
}

bool HeadPoseCalibrator::isUpdateReasonable(std::vector<float>& last_eye_centroid, 
                                                    std::vector<float>& eye_centroid) {
    bool is_reasonable = true;
    if (last_eye_centroid.size() != 2 || eye_centroid.size() != 2)
          return false;  
    
    float eye_y_diff = std::abs(last_eye_centroid[0] - eye_centroid[0]);
    float eye_x_diff = std::abs(last_eye_centroid[1] - eye_centroid[1]);
    // std::cout << "eye_x_diff:" << eye_x_diff << " eye_y_diff:" << eye_y_diff 
    // << "last_eye_centroid:" << last_eye_centroid[0] << "," << last_eye_centroid[1] 
    // << " eye_centroid:" << eye_centroid[0] << "," << eye_centroid[1]<< std::endl;
    if (std::abs(eye_y_diff) > KMaxEyeDifferY || std::abs(eye_x_diff) > KMaxEyeDifferX) {
        // eye_centroid = last_eye_centroid;
        is_reasonable = false;
    }
    return is_reasonable;
}

bool HeadPoseCalibrator::areAnglesReasonable(const std::vector<float>& angles) {
    for (const auto& angle : angles) {
        if (angle < KMinReasonbleAngle || angle > KMaxReasonbleAngle) {
            return false;
        }
    }
    return true;
}

int HeadPoseCalibrator::getHeadBufferSize() const {
    return headdata_deque_.size();
}

int HeadPoseCalibrator::getLeftEyeBufferSize() const {
    return leyedata_deque_.size();
}

int HeadPoseCalibrator::getRightEyeBufferSize() const {
    return reyedata_deque_.size();
}

}  // namespace tongxing
// example
// int main(int argc, char const *argv[]) {

//     int queue_length = 0;
//     int k_num = 0;
//     float cluster_radius = 0.0;
//     float threshold_50f = 0.0;
//     float threshold_100f = 0.0;
//     float threshold_longerf = 0.0;
//     // ---------------eyes config---------------
//     float eye_cluster_radius = 0.0;
//     float eye_threshold_50f = 0.0;
//     float eye_threshold_100f = 0.0;
//     float eye_threshold_longerf = 0.0;

//     readconfig("./config.json", queue_length, k_num, cluster_radius, \
//         threshold_50f, threshold_100f, threshold_longerf,
//         eye_cluster_radius, eye_threshold_50f, eye_threshold_100f, eye_threshold_longerf);
//     std::cout << "queue_length:" << queue_length << " k_num:" << k_num << std::endl;
//     std::cout << "cluster_radius:" << cluster_radius << " threshold_50f:" << threshold_50f << " threshold_100f:" << threshold_100f <<
//      " threshold_longerf:" << threshold_longerf << std::endl;
//     std::cout << "eye_cluster_radius:" << eye_cluster_radius << " eye_threshold_50f:" << eye_threshold_50f << " eye_threshold_100f:" << eye_threshold_100f <<
//      " eye_threshold_longerf:" << eye_threshold_longerf << std::endl;
//     HeadPoseCalibrator calibrator(queue_length, 50,k_num ,
//         cluster_radius, threshold_50f, threshold_100f, threshold_longerf,
//          eye_cluster_radius, eye_threshold_50f, eye_threshold_100f, eye_threshold_longerf);
//     calibrator.init();

//     std::string parse_path = std::string(argv[1]);

//     std::vector<std::string> suffix = {".json"};
//     std::vector<std::string> filelist;
//     read_files(parse_path, suffix, filelist);
//     std::cout << "filelist.size():" << filelist.size() << std::endl;
//     int cnt = 0;
//     for (auto& file : filelist) {
//         // std::cout << cnt<< " file:" << file << std::endl;
//         float headpitch = 0.0;
//         float headyaw = 0.0;
//         float headroll = 0.0;
//         float leyepitch = 0.0;
//         float leyeyaw = 0.0;
//         float reyepitch = 0.0;
//         float reyeyaw = 0.0;
//         cnt ++;
//         readheadpose(file.c_str(), headpitch, headyaw, headroll, leyepitch, leyeyaw, reyepitch, reyeyaw);
//         // std::cout << "read leye pitch:"<<leyepitch << " yaw:" << leyeyaw <<
//         //  " reye pitch:"<< reyepitch << " yaw:"<< reyeyaw << std::endl;
//         std::vector<float> head_angles = {headpitch, headyaw, headroll};
//         std::vector<float> lefteye_angles = {leyepitch, leyeyaw};
//         std::vector<float> righteye_angles = {reyepitch, reyeyaw};
//         std::vector<float> centroid;
//         std::vector<float> leyecentroid;
//         std::vector<float> reyecentroid;
//         calistatus status;
//         if (leyepitch == 0 || reyepitch == 0 || leyeyaw == 0 || reyeyaw == 0)
//         {
//             // std::cout << "eye not visuble, continue..." << std::endl;
//             continue;
//         }
//         // 在头和眼睛有一个满足要求时，返回成功，但需要继续标定直到头部和眼睛都满足要求为止
//         if (calibrator.execute(head_angles, lefteye_angles, righteye_angles, centroid, leyecentroid, reyecentroid, status)) {
//             std::cout << "cali cnt:" << cnt << std::endl;
//             std::cout << "cali success! centroid position:" << std::endl;
//             if (status.head_cali_finish) {
//                 std::cout << "Pitch: " << centroid[0] << std::endl;
//                 std::cout << "Yaw:   " << centroid[1] << std::endl;
//                 std::cout << "Roll:  " << centroid[2] << std::endl;
//             }
//             if (status.leye_cali_finish) {
//                 std::cout << "leye pitch: " << leyecentroid[0] << std::endl;
//                 std::cout << "leye yaw: " << leyecentroid[1] << std::endl;
//             }
//             if (status.reye_cali_finish) {
//                 std::cout << "reye pitch: " << reyecentroid[0] << std::endl;
//                 std::cout << "reye yaw: " << reyecentroid[1] << std::endl;
//             }

//             // 在头和眼睛都校准完成时，跳出循环
//             if (status.head_cali_finish && (status.leye_cali_finish || status.reye_cali_finish))
//                 break;
//         }

//     }

//     calibrator.clear();

//     return 0;
// }
