#include "CalibrationLogger.h"
#include <iostream>
#include <algorithm>
#include <iomanip>
#include <sstream>

namespace tongxing {

CalibrationLogger& CalibrationLogger::getInstance() {
    static CalibrationLogger instance;
    return instance;
}

CalibrationLogger::CalibrationLogger() 
    : output_interval_ms_(1000), enabled_(true), current_phase_(CalibrationPhase::PREPARING) {
    last_output_time_ = std::chrono::system_clock::now();
}

void CalibrationLogger::addObserver(std::shared_ptr<CalibrationLogObserver> observer) {
    observers_.push_back(observer);
}

void CalibrationLogger::removeObserver(std::shared_ptr<CalibrationLogObserver> observer) {
    observers_.erase(
        std::remove_if(observers_.begin(), observers_.end(),
            [&](const std::shared_ptr<CalibrationLogObserver>& o) {
                return o == observer;
            }),
        observers_.end());
}

void CalibrationLogger::recordProgress(int head_buffer, int left_eye_buffer, int right_eye_buffer) {
    if (!enabled_) return;

    // 分别统计各部分的帧数
    current_stats_.head_total_frames++;
    current_stats_.head_accepted_frames++;
    current_stats_.left_eye_total_frames++;
    current_stats_.left_eye_accepted_frames++;
    current_stats_.right_eye_total_frames++;
    current_stats_.right_eye_accepted_frames++;

    // 更新缓冲区大小和进度
    current_stats_.head_buffer_size = head_buffer;
    current_stats_.left_eye_buffer_size = left_eye_buffer;
    current_stats_.right_eye_buffer_size = right_eye_buffer;
    current_stats_.head_progress_percent = (head_buffer * 100) / TARGET_BUFFER_SIZE;
    current_stats_.left_eye_progress_percent = (left_eye_buffer * 100) / TARGET_BUFFER_SIZE;
    current_stats_.right_eye_progress_percent = (right_eye_buffer * 100) / TARGET_BUFFER_SIZE;

    // 计算预计完成时间
    calculateEstimatedTime();

    // 检查是否应该输出汇总信息
    checkAndOutputSummary();
}

void CalibrationLogger::recordFilter(const std::string& reason, const std::string& details, const std::string& part) {
    if (!enabled_) return;

    // 根据部位分别统计过滤帧数
    if (part == "head" || part.empty()) {
        current_stats_.head_total_frames++;
        current_stats_.head_filtered_frames++;
    }
    if (part == "left_eye" || part.empty()) {
        current_stats_.left_eye_total_frames++;
        current_stats_.left_eye_filtered_frames++;
    }
    if (part == "right_eye" || part.empty()) {
        current_stats_.right_eye_total_frames++;
        current_stats_.right_eye_filtered_frames++;
    }

    // 记录过滤原因
    if (current_stats_.filter_reasons.find(reason) != current_stats_.filter_reasons.end()) {
        current_stats_.filter_reasons[reason]++;
    } else {
        current_stats_.filter_reasons[reason] = 1;
    }

    // 检查是否应该输出汇总信息
    checkAndOutputSummary();
}

void CalibrationLogger::recordSuccess(bool head_complete, bool left_eye_complete, bool right_eye_complete) {
    if (!enabled_) return;
    
    // 更新阶段
    current_phase_ = CalibrationPhase::COMPLETED;
    
    // // 强制输出汇总信息
    // flushStats();
}

void CalibrationLogger::recordFailure(const std::string& reason) {
    if (!enabled_) return;
    
    // 更新阶段
    current_phase_ = CalibrationPhase::FAILED;
    
    // // 强制输出汇总信息
    // flushStats();
}

void CalibrationLogger::recordVehicleInfo(float speed, int gear) {
    if (!enabled_) return;
    
    last_vehicle_speed_ = speed;
    last_vehicle_gear_ = gear;
}

void CalibrationLogger::setOutputInterval(long interval_ms) {
    output_interval_ms_ = interval_ms;
}

void CalibrationLogger::setEnabled(bool enabled) {
    enabled_ = enabled;
}

void CalibrationLogger::flushStats() {
    if (!enabled_) return;
    
    // 更新进度百分比
    updateProgressPercent();
    
    // 通知所有观察者
    notifyObservers();
    
    // 更新最后输出时间
    last_output_time_ = std::chrono::system_clock::now();
}

void CalibrationLogger::resetStats() {
    current_stats_ = CalibrationStats();
    current_phase_ = CalibrationPhase::PREPARING;
}

void CalibrationLogger::setPhase(CalibrationPhase phase) {
    current_phase_ = phase;
}

void CalibrationLogger::checkAndOutputSummary() {
    auto now = std::chrono::system_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - last_output_time_).count();
    // std::cout << "checkAndOutputSummary: " << elapsed << "ms" << std::endl;
    if (elapsed >= output_interval_ms_) {
        // 更新进度百分比
        updateProgressPercent();
        
        // 通知所有观察者
        notifyObservers();
        
        // 更新最后输出时间
        last_output_time_ = now;
    }
}

void CalibrationLogger::notifyObservers() {
    for (const auto& observer : observers_) {
        observer->onSummaryLog(current_stats_, current_phase_);
    }

    // 输出后清空过滤原因统计，以便下次只显示新的过滤原因
    current_stats_.filter_reasons.clear();
}

void CalibrationLogger::calculateEstimatedTime() {
    // 找出进度最低的缓冲区
    int min_progress = std::min({
        current_stats_.head_progress_percent,
        current_stats_.left_eye_progress_percent,
        current_stats_.right_eye_progress_percent
    });
    
    // 如果进度为0或100%，则无法估计
    if (min_progress <= 0 || min_progress >= 100) {
        current_stats_.estimated_seconds_remaining = -1;
        return;
    }
    
    // 计算已经过去的时间
    auto now = std::chrono::system_clock::now();
    auto elapsed_seconds = std::chrono::duration_cast<std::chrono::seconds>(
        now - current_stats_.start_time).count();
    
    // 根据当前进度估计剩余时间
    if (elapsed_seconds > 0) {
        double seconds_per_percent = static_cast<double>(elapsed_seconds) / min_progress;
        int remaining_percent = 100 - min_progress;
        current_stats_.estimated_seconds_remaining = static_cast<int>(seconds_per_percent * remaining_percent);
    } else {
        current_stats_.estimated_seconds_remaining = -1;
    }
}

void CalibrationLogger::updateProgressPercent() {
    // 进度百分比已在recordProgress中更新
}

void CalibrationLogger::recordThresholds(float head_threshold, float left_eye_threshold, float right_eye_threshold) {
    if (!enabled_) return;

    current_stats_.head_threshold = head_threshold;
    current_stats_.left_eye_threshold = left_eye_threshold;
    current_stats_.right_eye_threshold = right_eye_threshold;
}

void CalibrationLogger::recordCurrentRatios(float head_ratio, float left_eye_ratio, float right_eye_ratio) {
    if (!enabled_) return;

    current_stats_.head_current_ratio = head_ratio;
    current_stats_.left_eye_current_ratio = left_eye_ratio;
    current_stats_.right_eye_current_ratio = right_eye_ratio;
}

void CalibrationLogger::recordPoseInfo(bool auto_calibration, float headpose_yaw, float headpose_pitch, float headpose_roll,
                                      float gaze_left_eye_yaw, float gaze_left_eye_pitch, float gaze_right_eye_yaw, float gaze_right_eye_pitch,
                                      float leye_score, float reye_score) {
    if (!enabled_) return;

    current_stats_.auto_calibration = auto_calibration;
    current_stats_.headpose_yaw = headpose_yaw;
    current_stats_.headpose_pitch = headpose_pitch;
    current_stats_.headpose_roll = headpose_roll;
    current_stats_.gaze_left_eye_yaw = gaze_left_eye_yaw;
    current_stats_.gaze_left_eye_pitch = gaze_left_eye_pitch;
    current_stats_.gaze_right_eye_yaw = gaze_right_eye_yaw;
    current_stats_.gaze_right_eye_pitch = gaze_right_eye_pitch;
    current_stats_.leye_uper_curve_score_mean = leye_score;
    current_stats_.reye_uper_curve_score_mean = reye_score;
}

void ConsoleLogObserver::onSummaryLog(const CalibrationStats& stats, CalibrationPhase phase) {
    std::stringstream ss;

    // 主要标定信息 - 保持原始格式
    ss << "[DMS CALIBRATION]:" << (stats.auto_calibration ? 1 : 0)
       << " [" << std::fixed << std::setprecision(2)
       << stats.headpose_yaw << "," << stats.headpose_pitch << "," << stats.headpose_roll << "]"
       << " [" << stats.gaze_left_eye_yaw << "," << stats.gaze_left_eye_pitch << "]"
       << " [" << stats.gaze_right_eye_yaw << "," << stats.gaze_right_eye_pitch << "]"
       << " [" << stats.leye_uper_curve_score_mean << "," << stats.reye_uper_curve_score_mean << "]";

    // 缓冲区进度 - 简洁格式
    ss << " | BUF: H[" << stats.head_buffer_size << "/" << CalibrationLogger::TARGET_BUFFER_SIZE << "]"
       << " L[" << stats.left_eye_buffer_size << "/" << CalibrationLogger::TARGET_BUFFER_SIZE << "]"
       << " R[" << stats.right_eye_buffer_size << "/" << CalibrationLogger::TARGET_BUFFER_SIZE << "]";

    // 阈值信息 - 显示比较关系格式
    ss << " | THR: H[";
    if (stats.head_threshold < 0) {
        ss << "-1";
    } else if (stats.head_current_ratio >= 0) {
        ss << std::setprecision(2) << stats.head_current_ratio << (stats.head_current_ratio >= stats.head_threshold ? ">" : "<") << stats.head_threshold;
    } else {
        ss << std::setprecision(2) << stats.head_threshold;
    }
    ss << "] L[";
    if (stats.left_eye_threshold < 0) {
        ss << "-1";
    } else if (stats.left_eye_current_ratio >= 0) {
        ss << std::setprecision(2) << stats.left_eye_current_ratio << (stats.left_eye_current_ratio >= stats.left_eye_threshold ? ">" : "<") << stats.left_eye_threshold;
    } else {
        ss << std::setprecision(2) << stats.left_eye_threshold;
    }
    ss << "] R[";
    if (stats.right_eye_threshold < 0) {
        ss << "-1";
    } else if (stats.right_eye_current_ratio >= 0) {
        ss << std::setprecision(2) << stats.right_eye_current_ratio << (stats.right_eye_current_ratio >= stats.right_eye_threshold ? ">" : "<") << stats.right_eye_threshold;
    } else {
        ss << std::setprecision(2) << stats.right_eye_threshold;
    }
    ss << "]";

    // 过滤统计 - 只显示主要过滤原因
    if (!stats.filter_reasons.empty()) {
        // 找到最多的过滤原因
        auto max_filter = std::max_element(stats.filter_reasons.begin(), stats.filter_reasons.end(),
            [](const auto& a, const auto& b) { return a.second < b.second; });
        ss << " | FILTER: " << max_filter->first << "(" << max_filter->second << ")";
    }
    
    // 主要过滤原因
    if (!stats.filter_reasons.empty()) {
        // 找出最频繁的过滤原因
        auto max_reason = std::max_element(
            stats.filter_reasons.begin(), stats.filter_reasons.end(),
            [](const auto& a, const auto& b) { return a.second < b.second; }
        );
        
        ss << " | MAIN FILTER: " << max_reason->first 
           << " (" << max_reason->second << ")";
    }
    
    std::cout << ss.str() << std::endl;
}

std::string ConsoleLogObserver::formatPhase(CalibrationPhase phase) {
    switch (phase) {
        case CalibrationPhase::PREPARING:
            return "CALIBRATION PREPARING...";
        case CalibrationPhase::IN_PROGRESS:
            return "CALIBRATION IN_PROGRESS...";
        case CalibrationPhase::COMPLETED:
            return "CALIBRATION COMPLETED...";
        case CalibrationPhase::FAILED:
            return "CALIBRATION FAILED...";
        default:
            return "CALIBRATION UNKNOWN...";
    }
}

std::string ConsoleLogObserver::formatProgress(int percent) {
    std::stringstream ss;
    ss << "[" << percent << "%]";
    return ss.str();
}

std::string ConsoleLogObserver::formatTime(int seconds) {
    int minutes = seconds / 60;
    seconds %= 60;
    
    std::stringstream ss;
    if (minutes > 0) {
        ss << minutes << " m";
    }
    ss << seconds << " s";
    
    return ss.str();
}

} // namespace tongxing
